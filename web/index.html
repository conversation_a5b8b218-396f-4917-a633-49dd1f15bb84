<!DOCTYPE html>
<html>
  <head>
    <!--
    If you are serving your web app in a path other than the root, change the
    href value below to reflect the base path you are serving from.

    The path provided below has to start and end with a slash "/" in order for
    it to work correctly.

    For more details:
    * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/base

    This is a placeholder for base href that will be replaced by the value of
    the `--base-href` argument provided to `flutter build`.
  -->
    <base href="$FLUTTER_BASE_HREF" />

    <meta charset="UTF-8" />
    <meta content="IE=Edge" http-equiv="X-UA-Compatible" />
    <meta
      name="description"
      content="<PERSON><PERSON> - Flutter Developer Portfolio"
    />

    <!-- iOS meta tags & icons -->
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black" />
    <meta
      name="apple-mobile-web-app-title"
      content="<PERSON><PERSON>folio"
    />
    <!-- Add the mobile-web-app-capable meta tag -->
    <meta name="mobile-web-app-capable" content="yes" />

    <!-- Favicon -->
    <link
      rel="icon"
      type="image/png"
      sizes="96x96"
      href="favicon/favicon-96x96.png"
    />
    <link rel="icon" type="image/svg+xml" href="favicon/favicon.svg" />
    <link rel="shortcut icon" href="favicon/favicon.ico" />
    <link
      rel="apple-touch-icon"
      sizes="180x180"
      href="favicon/apple-touch-icon.png"
    />
    <link rel="manifest" href="favicon/site.webmanifest" />
    <meta name="msapplication-TileColor" content="#2563EB" />
    <meta
      name="msapplication-TileImage"
      content="favicon/web-app-manifest-192x192.png"
    />
    <meta name="theme-color" content="#2563EB" />

    <title>Sufiyan Sakkeer - Portfolio</title>
    <link rel="manifest" href="manifest.json" />

    <!-- Initial loading animation styles -->
    <style>
      body {
        margin: 0;
        padding: 0;
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
        background-color: #121212;
        overflow: hidden;
      }

      #loading {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background-color: #121212;
        transition: opacity 0.5s ease-out;
        z-index: 9999;
      }

      .loading-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
      }

      .spinner {
        width: 80px;
        height: 80px;
        margin-bottom: 20px;
        position: relative;
      }

      .spinner-inner {
        position: absolute;
        width: 100%;
        height: 100%;
        border-radius: 50%;
        border: 4px solid transparent;
        border-top-color: #2196f3;
        animation: spin 1s linear infinite;
      }

      .spinner-inner:nth-child(2) {
        border-top-color: transparent;
        border-right-color: #03dac5;
        animation-delay: -0.25s;
      }

      .spinner-inner:nth-child(3) {
        border-top-color: transparent;
        border-right-color: transparent;
        border-bottom-color: #bb86fc;
        animation-delay: -0.5s;
      }

      .loading-text {
        color: #ffffff;
        font-size: 24px;
        font-weight: 500;
        margin-bottom: 8px;
      }

      .loading-subtitle {
        color: rgba(255, 255, 255, 0.7);
        font-size: 16px;
      }

      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }

      .loading-progress {
        width: 200px;
        height: 4px;
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: 2px;
        margin-top: 20px;
        overflow: hidden;
        position: relative;
      }

      .loading-progress-bar {
        position: absolute;
        top: 0;
        left: 0;
        height: 100%;
        width: 0%;
        background: linear-gradient(90deg, #2196f3, #03dac5, #bb86fc);
        background-size: 200% 100%;
        animation: progress-animation 2s ease-in-out infinite,
          gradient-animation 2s linear infinite;
        border-radius: 2px;
      }

      @keyframes progress-animation {
        0% {
          width: 0%;
        }
        50% {
          width: 70%;
        }
        100% {
          width: 100%;
        }
      }

      @keyframes gradient-animation {
        0% {
          background-position: 0% 0%;
        }
        100% {
          background-position: 200% 0%;
        }
      }
    </style>

    <!-- Font preloading -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />

    <!-- Preload and include Roboto font locally to avoid network issues -->
    <style>
      /* Local Roboto font definitions */
      @font-face {
        font-family: "Roboto";
        font-style: normal;
        font-weight: 400;
        src: local("Roboto"), local("Roboto-Regular"),
          url("assets/fonts/Roboto-Regular.ttf") format("truetype");
        font-display: swap;
      }
      @font-face {
        font-family: "Roboto";
        font-style: normal;
        font-weight: 500;
        src: local("Roboto Medium"), local("Roboto-Medium"),
          url("assets/fonts/Roboto-Medium.ttf") format("truetype");
        font-display: swap;
      }
      @font-face {
        font-family: "Roboto";
        font-style: normal;
        font-weight: 700;
        src: local("Roboto Bold"), local("Roboto-Bold"),
          url("assets/fonts/Roboto-Bold.ttf") format("truetype");
        font-display: swap;
      }
    </style>

    <script>
      // The value below is injected by flutter build, do not touch.
      // If not replaced during build, use null as a fallback
      var serviceWorkerVersion = "{{flutter_service_worker_version}}";

      // Check if the placeholder wasn't replaced (still contains the template markers)
      if (
        serviceWorkerVersion.includes("{{") &&
        serviceWorkerVersion.includes("}}")
      ) {
        console.log(
          "serviceWorkerVersion placeholder not replaced during build, using null"
        );
        serviceWorkerVersion = null;
      }
    </script>

    <!-- LaunchDarkly fallback mechanism -->
    <script>
      // Create a fallback for LaunchDarkly if it's blocked or fails
      window.launchDarklyFallback = {
        initialized: false,
        variation: function (key, defaultValue) {
          console.log("LaunchDarkly fallback used for feature flag:", key);
          return defaultValue;
        },
        identify: function () {
          console.log("LaunchDarkly fallback identify called");
          return Promise.resolve();
        },
        on: function () {
          // No-op event listener
          return { off: function () {} };
        },
      };

      // Error handler for LaunchDarkly loading
      window.handleLDError = function () {
        console.log("LaunchDarkly service unavailable, using fallback");
        window.LD = window.launchDarklyFallback;
      };

      // Try to load LaunchDarkly with error handling
      window.addEventListener(
        "error",
        function (event) {
          if (event.filename && event.filename.includes("launchdarkly")) {
            window.handleLDError();
            // Prevent the error from bubbling up
            event.preventDefault();
          }
        },
        true
      );
    </script>

    <!-- Firebase SDK scripts -->
    <script src="https://www.gstatic.com/firebasejs/9.6.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.6.1/firebase-firestore-compat.js"></script>

    <!-- Firebase configuration script -->
    <!--
      TEMPLATE NOTICE: Firebase configuration has been moved to environment variables
      for security. The Flutter app will handle Firebase initialization automatically.

      If you need to initialize Firebase directly in the web context:
      1. Create a .env file with your Firebase configuration
      2. Use a build process to inject environment variables
      3. Or configure Firebase through your Flutter app only

      This template approach keeps sensitive API keys out of your source code.
    -->
    <script>
      // Firebase will be initialized by the Flutter app
      // No hardcoded configuration needed here for security
      document.addEventListener("DOMContentLoaded", function () {
        console.log("Firebase will be initialized by Flutter app");
        window.firebaseInitialized = false; // Will be set by Flutter
      });
    </script>

    <!-- LaunchDarkly SDK -->
    <script
      src="https://unpkg.com/launchdarkly-js-client-sdk@2.19.0/dist/ldclient.min.js"
      onerror="handleLDError()"
    ></script>

    <!-- This script adds the flutter initialization JS code -->
    <script src="flutter.js" defer></script>
    <!-- Our custom Flutter initialization script -->
    <script src="main.js" defer></script>
  </head>
  <body>
    <!-- Initial loading animation -->
    <div id="loading">
      <div class="loading-container">
        <div class="spinner">
          <div class="spinner-inner"></div>
          <div class="spinner-inner"></div>
          <div class="spinner-inner"></div>
        </div>
        <div class="loading-text">Sufiyan Sakkeer</div>
        <div class="loading-subtitle">Flutter Developer</div>
        <div class="loading-progress">
          <div class="loading-progress-bar"></div>
        </div>
      </div>
    </div>

    <!-- Flutter initialization is now handled by flutter_bootstrap.js -->
  </body>
</html>
