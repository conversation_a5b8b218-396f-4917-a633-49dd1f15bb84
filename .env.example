# Firebase Configuration Template
# ================================
#
# This is a template for Firebase configuration environment variables.
#
# SETUP INSTRUCTIONS:
# 1. Copy this file: cp .env.example .env
# 2. Replace all placeholder values with your actual Firebase configuration
# 3. <PERSON>VE<PERSON> commit the .env file to version control
#
# HOW TO GET YOUR FIREBASE CONFIGURATION:
# 1. Go to https://console.firebase.google.com/
# 2. Select your project
# 3. Go to Project Settings (gear icon)
# 4. Scroll to "Your apps" section
# 5. Click on your web/android/iOS app to see the configuration

# ================================
# FIREBASE PROJECT CONFIGURATION
# ================================
# These values are shared across all platforms
FIREBASE_PROJECT_ID=your-project-id
FIREBASE_MESSAGING_SENDER_ID=your-messaging-sender-id
FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
FIREBASE_STORAGE_BUCKET=your-project.firebasestorage.app

# ================================
# FIREBASE WEB CONFIGURATION
# ================================
# Get these from your Firebase Web App configuration
FIREBASE_WEB_API_KEY=your-web-api-key-here
FIREBASE_WEB_APP_ID=your-web-app-id-here
FIREBASE_WEB_MEASUREMENT_ID=your-web-measurement-id-here

# ================================
# FIREBASE ANDROID CONFIGURATION
# ================================
# Get these from your Firebase Android App configuration
FIREBASE_ANDROID_API_KEY=your-android-api-key-here
FIREBASE_ANDROID_APP_ID=your-android-app-id-here

# ================================
# FIREBASE IOS CONFIGURATION
# ================================
# Get these from your Firebase iOS App configuration
FIREBASE_IOS_API_KEY=your-ios-api-key-here
FIREBASE_IOS_APP_ID=your-ios-app-id-here
FIREBASE_IOS_BUNDLE_ID=com.example.your-app

# ================================
# FIREBASE WINDOWS CONFIGURATION
# ================================
# Get these from your Firebase Windows App configuration
FIREBASE_WINDOWS_APP_ID=your-windows-app-id-here
FIREBASE_WINDOWS_MEASUREMENT_ID=your-windows-measurement-id-here

# ================================
# OTHER CONFIGURATION (OPTIONAL)
# ================================
# Add any other sensitive configuration here
# THIRD_PARTY_API_KEY=your-api-key
# DATABASE_URL=your-database-url
# SECRET_KEY=your-secret-key

# ================================
# EXAMPLE VALUES (DO NOT USE IN PRODUCTION)
# ================================
# Here are example formats for reference:
# FIREBASE_WEB_API_KEY=AIzaSyExample123456789
# FIREBASE_WEB_APP_ID=1:123456789:web:abcdef123456
# FIREBASE_PROJECT_ID=my-awesome-project
# FIREBASE_MESSAGING_SENDER_ID=123456789
# FIREBASE_AUTH_DOMAIN=my-awesome-project.firebaseapp.com
# FIREBASE_STORAGE_BUCKET=my-awesome-project.firebasestorage.app
